#!/usr/bin/env python3
"""
Test script to verify Notion button functionality.
Creates a test toggle with a webhook button without touching core logic.
"""

import os
from dotenv import load_dotenv
from notion_client import Client
import json

# Load environment variables
load_dotenv()

def test_notion_button():
    """Test creating a toggle with a button in Notion."""
    
    # Get credentials
    NOTION_API_TOKEN = os.getenv("NOTION_API_TOKEN")
    NOTION_PAGE_ID = os.getenv("NOTION_PAGE_ID")
    
    if not NOTION_API_TOKEN or not NOTION_PAGE_ID:
        print("❌ Missing NOTION_API_TOKEN or NOTION_PAGE_ID in .env")
        return False
    
    # Initialize client
    client = Client(auth=NOTION_API_TOKEN)
    
    # Format page ID if needed (add dashes)
    if len(NOTION_PAGE_ID) == 32 and "-" not in NOTION_PAGE_ID:
        NOTION_PAGE_ID = f"{NOTION_PAGE_ID[0:8]}-{NOTION_PAGE_ID[8:12]}-{NOTION_PAGE_ID[12:16]}-{NOTION_PAGE_ID[16:20]}-{NOTION_PAGE_ID[20:]}"
    
    print(f"📝 Testing on Notion page: {NOTION_PAGE_ID}")
    
    # Test article data
    test_article = {
        "url": "https://example.com/test-article",
        "title": "Test Article: Understanding RWA Tokenization"
    }
    
    # Create test blocks
    blocks = [
        # Header
        {
            "object": "block",
            "type": "heading_2",
            "heading_2": {
                "rich_text": [{
                    "type": "text",
                    "text": {"content": "🧪 Button Test Section"}
                }]
            }
        },
        
        # Toggle with article info and button
        {
            "object": "block",
            "type": "toggle",
            "toggle": {
                "rich_text": [{
                    "type": "text",
                    "text": {
                        "content": "📰 " + test_article["title"],
                        "link": {"url": test_article["url"]}
                    }
                }],
                "children": [
                    # Metadata
                    {
                        "object": "block",
                        "type": "paragraph",
                        "paragraph": {
                            "rich_text": [{
                                "type": "text",
                                "text": {"content": "Published by: Test Publisher • Jan 22, 2025 • 10:30 AM PST"},
                                "annotations": {"italic": True}
                            }]
                        }
                    },
                    
                    # Summary
                    {
                        "object": "block",
                        "type": "paragraph",
                        "paragraph": {
                            "rich_text": [{
                                "type": "text",
                                "text": {"content": "This is a test article to verify button functionality. The button below should send article data to a webhook."}
                            }]
                        }
                    },
                    
                    # Button (as a paragraph with a link for now - Notion API button support varies)
                    {
                        "object": "block",
                        "type": "paragraph",
                        "paragraph": {
                            "rich_text": [{
                                "type": "text",
                                "text": {
                                    "content": "🔗 Generate LinkedIn Draft",
                                    "link": {
                                        "url": f"https://webhook.site/test?article_url={test_article['url']}&article_title={test_article['title']}"
                                    }
                                },
                                "annotations": {
                                    "bold": True,
                                    "color": "blue"
                                }
                            }]
                        }
                    },
                    
                    # Alternative: Callout with webhook info
                    {
                        "object": "block",
                        "type": "callout",
                        "callout": {
                            "rich_text": [{
                                "type": "text",
                                "text": {"content": f"Webhook payload: {json.dumps(test_article, indent=2)}"}
                            }],
                            "icon": {"emoji": "🔧"},
                            "color": "blue_background"
                        }
                    }
                ]
            }
        },
        
        # Note about button limitations
        {
            "object": "block",
            "type": "callout",
            "callout": {
                "rich_text": [{
                    "type": "text",
                    "text": {"content": "Note: Notion API has limited button support. The link above simulates a button. In the Notion UI, you can convert this to an actual button block or use automation."}
                }],
                "icon": {"emoji": "ℹ️"},
                "color": "gray_background"
            }
        }
    ]
    
    try:
        # Append blocks to page
        response = client.blocks.children.append(
            block_id=NOTION_PAGE_ID,
            children=blocks
        )
        
        print("✅ Successfully created test toggle with button!")
        print("📍 Check your Notion page to see the result")
        print("\n💡 Next steps:")
        print("1. Go to webhook.site to create a test endpoint")
        print("2. Update the webhook URL in the code")
        print("3. Click the button link to test the webhook")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Testing Notion button functionality...\n")
    success = test_notion_button()
    
    if not success:
        print("\n❌ Test failed!")
    else:
        print("\n✅ Test completed!")