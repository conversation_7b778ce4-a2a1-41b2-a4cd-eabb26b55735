#!/usr/bin/env python3
"""
Test script to verify that the timezone fix is working correctly.
This simulates the database behavior and tests the custom datetime parser.
"""

import sys
import os
import logging
import io
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.storage.pg_utils import parse_datetime_with_tz, UTC_TZ
from core.timezone_config import ensure_utc

def test_custom_datetime_parser():
    """Test that the custom datetime parser returns timezone-aware datetimes."""
    print("Testing custom datetime parser...")
    
    # Test with a naive datetime string (simulating PostgreSQL output)
    test_datetime_str = "2025-07-21 09:31:41"
    
    # This simulates what psycopg2 would do with our custom parser
    result = parse_datetime_with_tz(test_datetime_str, None)
    
    print(f"Input: {test_datetime_str}")
    print(f"Parsed result: {result}")
    print(f"Has timezone: {result.tzinfo is not None if result else False}")
    
    if result and result.tzinfo:
        print(f"Timezone: {result.tzinfo}")
        print("✅ Custom parser returns timezone-aware datetime")
        return True
    else:
        print("❌ Custom parser failed to return timezone-aware datetime")
        return False

def test_ensure_utc_with_timezone_aware():
    """Test that ensure_utc doesn't log warnings for timezone-aware datetimes."""
    print("\nTesting ensure_utc with timezone-aware datetime...")
    
    # Create a timezone-aware datetime (simulating what our fixed PostgreSQL would return)
    tz_aware_dt = datetime(2025, 7, 21, 9, 31, 41, tzinfo=UTC_TZ)
    
    # Capture log output
    log_capture = io.StringIO()
    handler = logging.StreamHandler(log_capture)
    logger = logging.getLogger('core.timezone_config')
    logger.addHandler(handler)
    logger.setLevel(logging.WARNING)
    
    # This should NOT trigger a warning
    result = ensure_utc(tz_aware_dt)
    
    # Check if warning was logged
    log_output = log_capture.getvalue()
    
    print(f"Input: {tz_aware_dt}")
    print(f"Result: {result}")
    
    if log_output:
        print(f"❌ Unexpected warning logged: {log_output.strip()}")
        return False
    else:
        print("✅ No warning logged for timezone-aware datetime")
        return True

def test_ensure_utc_with_naive():
    """Test that ensure_utc still logs warnings for naive datetimes (for comparison)."""
    print("\nTesting ensure_utc with naive datetime (should still warn)...")
    
    # Create a naive datetime
    naive_dt = datetime(2025, 7, 21, 9, 31, 41)
    
    # Capture log output
    log_capture = io.StringIO()
    handler = logging.StreamHandler(log_capture)
    logger = logging.getLogger('core.timezone_config')
    logger.addHandler(handler)
    logger.setLevel(logging.WARNING)
    
    # This SHOULD trigger a warning
    result = ensure_utc(naive_dt)
    
    # Check if warning was logged
    log_output = log_capture.getvalue()
    
    print(f"Input: {naive_dt}")
    print(f"Result: {result}")
    
    if log_output:
        print(f"✅ Expected warning logged: {log_output.strip()}")
        return True
    else:
        print("❌ No warning logged for naive datetime (unexpected)")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("TIMEZONE FIX VERIFICATION")
    print("=" * 60)
    
    tests = [
        test_custom_datetime_parser,
        test_ensure_utc_with_timezone_aware,
        test_ensure_utc_with_naive
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with error: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The timezone fix is working correctly.")
        print("\nWhat this means:")
        print("- PostgreSQL will now return timezone-aware datetime objects")
        print("- The ensure_utc() function won't log warnings for database-retrieved datetimes")
        print("- You should see significantly fewer timezone warnings in the GitHub Actions logs")
    else:
        print("❌ Some tests failed. The timezone fix may not be working correctly.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
